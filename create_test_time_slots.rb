#!/usr/bin/env ruby

# Script to create test faculty time slots
# Run with: rails runner create_test_time_slots.rb

puts "Creating test faculty time slots..."

# Find a faculty user (teacher)
faculty_user = User.joins(:enrollments).where(enrollments: { type: 'TeacherEnrollment', workflow_state: 'active' }).first

if faculty_user.nil?
  puts "No faculty user found. Creating one..."
  # Create a test faculty user if none exists
  faculty_user = User.create!(
    name: "Dr. Test Faculty",
    email: "<EMAIL>",
    workflow_state: 'active'
  )
  
  # Create a teacher enrollment for this user
  # You might need to adjust this based on your course structure
  course = Course.first || Course.create!(name: "Test Course", workflow_state: 'available')
  course.enrollments.create!(user: faculty_user, type: 'TeacherEnrollment', workflow_state: 'active')
end

puts "Using faculty user: #{faculty_user.name} (ID: #{faculty_user.id})"

# Clear existing time slots for this faculty
faculty_user.faculty_time_slots.destroy_all

# Create recurring time slots for Monday to Friday
%w[Monday Tuesday Wednesday Thursday Friday].each do |day|
  # Morning slot: 9:00 AM - 11:00 AM (2 hours)
  faculty_user.faculty_time_slots.create!(
    day_of_week: day,
    start_time: Time.zone.parse("09:00"),
    end_time: Time.zone.parse("11:00"),
    is_recurring: true,
    is_available: true,
    notes: "Morning consultation hours"
  )

  # Afternoon slot: 2:00 PM - 4:00 PM (2 hours)
  faculty_user.faculty_time_slots.create!(
    day_of_week: day,
    start_time: Time.zone.parse("14:00"),
    end_time: Time.zone.parse("16:00"),
    is_recurring: true,
    is_available: true,
    notes: "Afternoon consultation hours"
  )
  
  puts "Created time slots for #{day}"
end

# Create a specific date slot for tomorrow (3 hours)
tomorrow = Date.tomorrow
faculty_user.faculty_time_slots.create!(
  day_of_week: tomorrow.strftime('%A'),
  start_time: Time.zone.parse("10:00"),
  end_time: Time.zone.parse("13:00"),
  specific_date: tomorrow,
  is_recurring: false,
  is_available: true,
  notes: "Special consultation session"
)

puts "Created specific date slot for #{tomorrow}"

total_slots = faculty_user.faculty_time_slots.count
puts "Total time slots created: #{total_slots}"
puts "Available slots: #{faculty_user.faculty_time_slots.available.count}"
puts "Recurring slots: #{faculty_user.faculty_time_slots.recurring.count}"
puts "Specific date slots: #{faculty_user.faculty_time_slots.specific_date.count}"

puts "Test data creation completed!"
